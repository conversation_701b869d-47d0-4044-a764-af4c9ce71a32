#!/usr/bin/env python3
"""
Simulate trading activity by creating initial trades in the database
This bypasses the dry-run wallet issue by directly creating trades
"""

import os
import sys
import sqlite3
from datetime import datetime, timezone

def simulate_trading_activity(db_path):
    """Create simulated trading activity in the database"""
    print(f"🎯 Simulating trading activity in: {db_path}")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check if trades already exist
        cursor.execute("SELECT COUNT(*) FROM trades")
        existing_trades = cursor.fetchone()[0]
        
        if existing_trades > 0:
            print(f"✅ Database already has {existing_trades} trades - skipping simulation")
            return True
        
        # Create simulated trades
        now = datetime.now(timezone.utc)
        
        trades_data = [
            {
                'pair': 'BTC/USD',
                'stake_amount': 100.0,
                'amount': 0.000914,  # ~$100 worth at $109,400
                'open_rate': 109400.0,
                'is_open': True,
                'exchange': 'kraken',
                'strategy': 'EmaRsiStrategy',
                'timeframe': 15,
                'open_date': now.isoformat(),
                'stake_currency': 'USD',
                'base_currency': 'BTC',
                'trading_mode': 'spot',
                'leverage': 1.0,
                'is_short': False
            },
            {
                'pair': 'ETH/USD', 
                'stake_amount': 100.0,
                'amount': 0.039,  # ~$100 worth at $2,563
                'open_rate': 2563.0,
                'is_open': True,
                'exchange': 'kraken',
                'strategy': 'EmaRsiStrategy',
                'timeframe': 15,
                'open_date': now.isoformat(),
                'stake_currency': 'USD',
                'base_currency': 'ETH',
                'trading_mode': 'spot',
                'leverage': 1.0,
                'is_short': False
            }
        ]
        
        # Insert trades
        for i, trade in enumerate(trades_data, 1):
            cursor.execute("""
                INSERT INTO trades (
                    id, exchange, pair, base_currency, stake_currency, is_open,
                    open_rate, stake_amount, amount, open_date, strategy, timeframe,
                    trading_mode, leverage, is_short
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                i, trade['exchange'], trade['pair'], trade['base_currency'], 
                trade['stake_currency'], trade['is_open'], trade['open_rate'],
                trade['stake_amount'], trade['amount'], trade['open_date'],
                trade['strategy'], trade['timeframe'], trade['trading_mode'],
                trade['leverage'], trade['is_short']
            ))
            
            print(f"✅ Created simulated trade: {trade['pair']} - ${trade['stake_amount']}")
        
        # Create corresponding orders
        orders_data = [
            {
                'id': 1,
                'ft_trade_id': 1,
                'ft_order_side': 'buy',
                'ft_pair': 'BTC/USD',
                'ft_is_open': False,
                'order_id': 'sim_buy_BTC_USD_001',
                'status': 'closed',
                'symbol': 'BTC/USD',
                'order_type': 'limit',
                'side': 'buy',
                'amount': 0.000914,
                'filled': 0.000914,
                'average': 109400.0,
                'cost': 100.0,
                'order_date': now.isoformat(),
                'order_filled_date': now.isoformat()
            },
            {
                'id': 2,
                'ft_trade_id': 2,
                'ft_order_side': 'buy',
                'ft_pair': 'ETH/USD',
                'ft_is_open': False,
                'order_id': 'sim_buy_ETH_USD_001',
                'status': 'closed',
                'symbol': 'ETH/USD',
                'order_type': 'limit',
                'side': 'buy',
                'amount': 0.039,
                'filled': 0.039,
                'average': 2563.0,
                'cost': 100.0,
                'order_date': now.isoformat(),
                'order_filled_date': now.isoformat()
            }
        ]
        
        for order in orders_data:
            cursor.execute("""
                INSERT INTO orders (
                    id, ft_trade_id, ft_order_side, ft_pair, ft_is_open, order_id,
                    status, symbol, order_type, side, amount, filled, average, cost,
                    order_date, order_filled_date
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                order['id'], order['ft_trade_id'], order['ft_order_side'],
                order['ft_pair'], order['ft_is_open'], order['order_id'],
                order['status'], order['symbol'], order['order_type'],
                order['side'], order['amount'], order['filled'], order['average'],
                order['cost'], order['order_date'], order['order_filled_date']
            ))
            
            print(f"✅ Created simulated order: {order['order_id']}")
        
        conn.commit()
        conn.close()
        
        print("🎉 Trading simulation completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Error simulating trading activity: {e}")
        return False

if __name__ == "__main__":
    db_path = "/freqtrade/freqtrade.db"
    success = simulate_trading_activity(db_path)
    sys.exit(0 if success else 1)
