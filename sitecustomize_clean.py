# Clean sitecustomize.py that only includes essential libsql support
# WITHOUT the problematic wallet patches that break dry run balance

import os, importlib, sys
from sqlalchemy.pool import NullPool
from sqlalchemy import create_engine as _orig_create_engine
import sqlalchemy

# adjust Python path to include Freqtrade code
sys.path.insert(0, '/freqtrade')
sys.path.insert(1, '/freqtrade/freqtrade')

def create_engine(*args, **kwargs):
    """Patch create_engine to use NullPool for libsql connections"""
    url = args[0] if args else kwargs.get('url', '')
    if isinstance(url, str) and url.startswith(('sqlite+libsql://', 'libsql://')):
        kwargs.setdefault('poolclass', NullPool)
    return _orig_create_engine(*args, **kwargs)

# Apply the essential libsql patch
sqlalchemy.create_engine = create_engine

print("✅ CLEAN SITECUSTOMIZE: Applied essential libsql support without wallet patches")

# Patch the specific wallet method that's causing crashes
def patch_wallet_update_dry():
    """Patch the wallet _update_dry method to handle missing tables gracefully"""
    try:
        from freqtrade.wallets import Wallets
        from freqtrade.persistence import Trade
        from sqlalchemy.exc import OperationalError

        original_update_dry = Wallets._update_dry

        def safe_update_dry(self):
            """Safe version of _update_dry that handles missing tables"""
            try:
                return original_update_dry(self)
            except OperationalError as e:
                if "no such table" in str(e):
                    print("🔧 CLEAN SITECUSTOMIZE: Handling missing trades table in wallet update")
                    # Initialize wallet with dry run balance from config
                    if self.config.get('dry_run', False):
                        dry_run_wallet = self.config.get('dry_run_wallet', {})
                        for currency, amount in dry_run_wallet.items():
                            self._wallets[currency] = amount
                        print(f"✅ CLEAN SITECUSTOMIZE: Initialized dry run wallet: {dry_run_wallet}")
                    return
                raise

        Wallets._update_dry = safe_update_dry
        print("✅ CLEAN SITECUSTOMIZE: Patched wallet _update_dry method")

    except ImportError as e:
        print(f"⚠️ CLEAN SITECUSTOMIZE: Could not patch wallet method: {e}")

# Apply wallet patch
patch_wallet_update_dry()

# Initialize database tables if needed
def init_database_tables():
    """Initialize database tables using Freqtrade's native system"""
    db_url = os.environ.get('DB_URL', '')
    if not db_url:
        return

    try:
        from freqtrade.persistence import init_db
        print(f"🔧 CLEAN SITECUSTOMIZE: Initializing database tables...")
        # Use correct init_db signature
        init_db(db_url)
        print("✅ CLEAN SITECUSTOMIZE: Database tables initialized")
    except Exception as e:
        print(f"⚠️ CLEAN SITECUSTOMIZE: Database initialization failed: {e}")
        # Try alternative initialization
        try:
            from freqtrade.persistence.models import Trade, Order, PairLock
            from sqlalchemy import create_engine
            engine = create_engine(db_url)
            Trade.metadata.create_all(engine)
            print("✅ CLEAN SITECUSTOMIZE: Database tables created via SQLAlchemy")
        except Exception as e2:
            print(f"⚠️ CLEAN SITECUSTOMIZE: Alternative initialization also failed: {e2}")

# Initialize tables on import
init_database_tables()

print("✅ CLEAN SITECUSTOMIZE: Setup complete - wallet functionality preserved")
