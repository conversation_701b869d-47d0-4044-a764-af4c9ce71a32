-- Complete Freqtrade table schema for Turso
-- This creates all required tables with proper structure

CREATE TABLE IF NOT EXISTS trades (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    exchange VARCHAR(25) NOT NULL,
    pair VARCHAR(25) NOT NULL,
    base_currency VARCHAR(25),
    stake_currency VARCHAR(25),
    is_open BOOLEAN NOT NULL DEFAULT 1,
    fee_open FLOAT DEFAULT 0.0,
    fee_open_cost FLOAT,
    fee_open_currency VARCHAR(25),
    fee_close FLOAT DEFAULT 0.0,
    fee_close_cost FLOAT,
    fee_close_currency VARCHAR(25),
    open_rate FLOAT,
    open_rate_requested FLOAT,
    open_trade_value FLOAT,
    close_rate FLOAT,
    close_rate_requested FLOAT,
    realized_profit FLOAT,
    close_profit FLOAT,
    close_profit_abs FLOAT,
    stake_amount FLOAT NOT NULL,
    max_stake_amount FLOAT,
    amount FLOAT,
    amount_requested FLOAT,
    open_date DATETIME NOT NULL,
    close_date DATETIME,
    stop_loss FLOAT,
    stop_loss_pct FLOAT,
    initial_stop_loss FLOAT,
    initial_stop_loss_pct FLOAT,
    is_stop_loss_trailing BOOLEAN,
    max_rate FLOAT,
    min_rate FLOAT,
    exit_reason VARCHAR(100),
    exit_order_status VARCHAR(100),
    strategy VARCHAR(100),
    enter_tag VARCHAR(100),
    timeframe INTEGER,
    trading_mode VARCHAR(100),
    amount_precision FLOAT,
    price_precision FLOAT,
    precision_mode INTEGER,
    precision_mode_price INTEGER,
    contract_size FLOAT,
    leverage FLOAT DEFAULT 1.0,
    is_short BOOLEAN DEFAULT 0,
    liquidation_price FLOAT,
    interest_rate FLOAT,
    funding_fees FLOAT,
    funding_fee_running FLOAT,
    record_version INTEGER DEFAULT 1
);

CREATE TABLE IF NOT EXISTS KeyValueStore (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    key VARCHAR(255) NOT NULL UNIQUE,
    value_type VARCHAR(255),
    string_value TEXT,
    datetime_value DATETIME,
    float_value FLOAT,
    int_value INTEGER
);

CREATE TABLE IF NOT EXISTS pairlocks (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    pair VARCHAR(25) NOT NULL,
    reason VARCHAR(255),
    lock_time DATETIME NOT NULL,
    lock_end_time DATETIME NOT NULL,
    active BOOLEAN NOT NULL DEFAULT 1
);

CREATE TABLE IF NOT EXISTS orders (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    ft_trade_id INTEGER,
    ft_order_side VARCHAR(25) NOT NULL,
    ft_pair VARCHAR(25) NOT NULL,
    ft_is_open BOOLEAN NOT NULL DEFAULT 1,
    order_id VARCHAR(255) NOT NULL,
    status VARCHAR(255),
    symbol VARCHAR(25),
    order_type VARCHAR(50),
    side VARCHAR(25),
    amount FLOAT,
    filled FLOAT,
    average FLOAT,
    cost FLOAT,
    order_date DATETIME,
    order_filled_date DATETIME,
    order_update_date DATETIME,
    leverage FLOAT,
    is_short BOOLEAN,
    funding_fee FLOAT,
    funding_fee_running FLOAT
);

CREATE TABLE IF NOT EXISTS trade_custom_data (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    ft_trade_id INTEGER NOT NULL,
    cd_key VARCHAR(255) NOT NULL,
    cd_value TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
