# Dockerfile.freqtrade
FROM freqtradeorg/freqtrade:stable
# Install the libsql dialect so SQLAlchemy accepts sqlite+libsql://…
RUN pip install libsql-client sqlalchemy-libsql

# Install dependencies for Turso CLI
USER root
RUN apt-get update && apt-get install -y xz-utils curl && rm -rf /var/lib/apt/lists/*

# Install Turso CLI for direct database access (ignore browser open error)
RUN curl -sSfL https://get.tur.so/install.sh | bash || true
ENV PATH="/root/.turso:${PATH}"

# Switch back to ftuser
USER ftuser
# Copy the sitecustomize patch from bot-manager directory
COPY sitecustomize.py /usr/local/lib/python3.12/site-packages/sitecustomize.py
# Override persistence migrations to ensure schema created
COPY freqtrade/freqtrade/persistence/migrations.py /freqtrade/freqtrade/persistence/migrations.py
# CRITICAL: Replace the actual key_value_store.py file with our patched version
COPY freqtrade/freqtrade/persistence/key_value_store.py /freqtrade/freqtrade/persistence/key_value_store.py
# CRITICAL: Replace the entrypoint script with our enhanced version
COPY entrypoint.sh /freqtrade/entrypoint.sh
# CRITICAL: Replace freqtradebot.py with our patched version that handles database errors
COPY freqtradebot_patched.py /freqtrade/freqtrade/freqtradebot.py