#!/usr/bin/env python3
"""
Proper Turso database initialization for Freqtrade
This uses Freqtrade's native models to create tables correctly
"""

import os
import sys
import time

def init_turso_database(db_url):
    """Initialize Turso database using Freqtrade's native models"""
    print(f"🔧 Initializing Turso database...")
    print(f"Database URL: {db_url}")
    
    try:
        # Import Freqtrade's database models
        from freqtrade.persistence.models import init_db, ModelBase
        from sqlalchemy import create_engine, inspect
        
        print("📦 Imported Freqtrade models successfully")
        
        # Create engine with Turso-optimized settings
        engine = create_engine(
            db_url,
            echo=False,
            pool_pre_ping=True,
            pool_recycle=300,  # Shorter recycle for Turso
            connect_args={
                "timeout": 60,
                "check_same_thread": False
            }
        )
        
        print("🔗 Created database engine")
        
        # Method 1: Use Freqtrade's init_db
        print("🚀 Method 1: Using Freqtrade's init_db...")
        try:
            init_db(db_url)
            print("✅ Freqtrade init_db completed")
        except Exception as e:
            print(f"⚠️ Freqtrade init_db failed: {e}")
        
        # Method 2: Direct table creation using ModelBase
        print("🚀 Method 2: Using ModelBase.metadata.create_all...")
        try:
            ModelBase.metadata.create_all(engine)
            print("✅ ModelBase table creation completed")
        except Exception as e:
            print(f"⚠️ ModelBase creation failed: {e}")
        
        # Method 3: Force table creation with explicit transaction
        print("🚀 Method 3: Force creation with explicit transaction...")
        try:
            with engine.begin() as conn:
                ModelBase.metadata.create_all(bind=conn)
                print("✅ Force creation with transaction completed")
        except Exception as e:
            print(f"⚠️ Force creation failed: {e}")
        
        # Verify tables exist
        print("🔍 Verifying tables...")
        time.sleep(2)  # Give Turso time to sync
        
        try:
            inspector = inspect(engine)
            tables = inspector.get_table_names()
            print(f"📋 Tables found: {tables}")
            
            if tables:
                print("✅ SUCCESS: Tables created in Turso!")
                
                # Test basic operations
                with engine.connect() as conn:
                    for table in ['trades', 'KeyValueStore', 'pairlocks']:
                        if table in tables:
                            try:
                                result = conn.execute(f"SELECT COUNT(*) FROM {table}")
                                count = result.scalar()
                                print(f"✅ Table {table}: {count} rows")
                            except Exception as e:
                                print(f"⚠️ Table {table} test failed: {e}")
                
                return True
            else:
                print("❌ No tables found after creation")
                return False
                
        except Exception as e:
            print(f"❌ Verification failed: {e}")
            return False
            
    except Exception as e:
        print(f"❌ Fatal error: {e}")
        return False

if __name__ == "__main__":
    db_url = os.environ.get('DB_URL')
    if not db_url:
        print("❌ DB_URL environment variable required")
        sys.exit(1)
    
    success = init_turso_database(db_url)
    if success:
        print("🎉 Turso database initialization completed successfully!")
        sys.exit(0)
    else:
        print("💥 Turso database initialization failed!")
        sys.exit(1)
