#!/usr/bin/env python3

import os
import sys
import subprocess

# Add freqtrade to Python path
sys.path.insert(0, '/freqtrade')

def init_database():
    """Initialize database using Freqtrade's built-in system"""
    db_url = os.environ.get('DB_URL', '')
    if not db_url:
        print("No DB_URL provided")
        return False
    
    try:
        # Import Freqtrade's database initialization
        from freqtrade.persistence import init_db
        
        print(f"🔧 Initializing database: {db_url[:50]}...")
        
        # Initialize database with Freqtrade's own system
        init_db(db_url, clean_open_orders=False)
        print("✅ Database initialized successfully")
        
        # Verify tables exist
        from sqlalchemy import create_engine, text
        engine = create_engine(db_url)
        with engine.connect() as conn:
            result = conn.execute(text('SELECT name FROM sqlite_master WHERE type="table"'))
            tables = [row[0] for row in result]
            print(f"📊 Tables available: {tables}")
            
            if 'trades' in tables:
                print("[ENTRYPOINT] ✓ DATABASE READY")
                return True
            else:
                print("[ENTRYPOINT] ⚠ DATABASE NOT READY")
                return False
                
    except Exception as e:
        print(f"❌ Database initialization failed: {e}")
        print("[ENTRYPOINT] ⚠ DATABASE NOT READY")
        return False

def main():
    """Main entrypoint function"""
    print("🚀 Starting Freqtrade with clean database initialization...")
    
    # Initialize database
    db_ready = init_database()
    
    # Start Freqtrade
    print("🎯 Starting Freqtrade...")
    
    freqtrade_args = [
        'python', '-m', 'freqtrade', 'trade',
        '--config', '/freqtrade/config.json',
        '--db-url', os.environ.get('DB_URL', ''),
        '--strategy-path', '/freqtrade/user_data/strategies'
    ]
    
    try:
        subprocess.run(freqtrade_args, check=True)
    except subprocess.CalledProcessError as e:
        print(f"❌ Freqtrade exited with error: {e}")
        sys.exit(1)
    except KeyboardInterrupt:
        print("🛑 Freqtrade stopped by user")
        sys.exit(0)

if __name__ == '__main__':
    main()
