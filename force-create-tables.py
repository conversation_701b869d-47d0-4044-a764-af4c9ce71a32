#!/usr/bin/env python3
"""
Force create database tables for Freqtrade in Turso
This bypasses Freqtrade's normal initialization and creates tables directly
"""

import os
import sys
from sqlalchemy import create_engine, text

def force_create_tables(db_url):
    """Force create all required tables in Turso database"""
    print(f"🔧 Force creating tables in database...")
    
    # Create engine with Turso-specific settings
    engine = create_engine(
        db_url,
        echo=False,
        pool_pre_ping=True,
        pool_recycle=3600,
        connect_args={
            "timeout": 30,
            "check_same_thread": False
        }
    )
    
    # Complete table creation SQL
    create_tables_sql = """
    -- Create trades table
    CREATE TABLE IF NOT EXISTS trades (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        exchange VARCHAR(25) NOT NULL,
        pair VARCHAR(25) NOT NULL,
        base_currency VARCHAR(25),
        stake_currency VARCHAR(25),
        is_open BOOLEAN NOT NULL DEFAULT 1,
        fee_open FLOAT,
        fee_open_cost FLOAT,
        fee_open_currency VARCHAR(25),
        fee_close FLOAT,
        fee_close_cost FLOAT,
        fee_close_currency VARCHAR(25),
        open_rate FLOAT,
        open_rate_requested FLOAT,
        open_trade_value FLOAT,
        close_rate FLOAT,
        close_rate_requested FLOAT,
        realized_profit FLOAT,
        close_profit FLOAT,
        close_profit_abs FLOAT,
        stake_amount FLOAT NOT NULL,
        max_stake_amount FLOAT,
        amount FLOAT,
        amount_requested FLOAT,
        open_date DATETIME NOT NULL,
        close_date DATETIME,
        stop_loss FLOAT,
        stop_loss_pct FLOAT,
        initial_stop_loss FLOAT,
        initial_stop_loss_pct FLOAT,
        is_stop_loss_trailing BOOLEAN,
        max_rate FLOAT,
        min_rate FLOAT,
        exit_reason VARCHAR(100),
        exit_order_status VARCHAR(100),
        strategy VARCHAR(100),
        enter_tag VARCHAR(100),
        timeframe INTEGER,
        trading_mode VARCHAR(100),
        amount_precision FLOAT,
        price_precision FLOAT,
        precision_mode INTEGER,
        precision_mode_price INTEGER,
        contract_size FLOAT,
        leverage FLOAT,
        is_short BOOLEAN,
        liquidation_price FLOAT,
        interest_rate FLOAT,
        funding_fees FLOAT,
        funding_fee_running FLOAT,
        record_version INTEGER DEFAULT 1
    );

    -- Create KeyValueStore table
    CREATE TABLE IF NOT EXISTS "KeyValueStore" (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        "key" VARCHAR(255) NOT NULL UNIQUE,
        value_type VARCHAR(255),
        string_value TEXT,
        datetime_value DATETIME,
        float_value FLOAT,
        int_value INTEGER
    );

    -- Create pairlocks table
    CREATE TABLE IF NOT EXISTS pairlocks (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        pair VARCHAR(25) NOT NULL,
        reason VARCHAR(255),
        lock_time DATETIME NOT NULL,
        lock_end_time DATETIME NOT NULL,
        active BOOLEAN NOT NULL DEFAULT 1
    );

    -- Create orders table
    CREATE TABLE IF NOT EXISTS orders (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        ft_trade_id INTEGER,
        ft_order_side VARCHAR(25) NOT NULL,
        ft_pair VARCHAR(25) NOT NULL,
        ft_is_open BOOLEAN NOT NULL DEFAULT 1,
        order_id VARCHAR(255) NOT NULL,
        status VARCHAR(255),
        symbol VARCHAR(25),
        order_type VARCHAR(50),
        side VARCHAR(25),
        amount FLOAT,
        filled FLOAT,
        average FLOAT,
        cost FLOAT,
        order_date DATETIME,
        order_filled_date DATETIME,
        order_update_date DATETIME,
        leverage FLOAT,
        is_short BOOLEAN,
        funding_fee FLOAT,
        funding_fee_running FLOAT
    );
    """
    
    try:
        # Execute table creation in a single transaction
        with engine.begin() as conn:
            # Split and execute each CREATE TABLE statement
            statements = [stmt.strip() for stmt in create_tables_sql.split(';') if stmt.strip()]
            
            for stmt in statements:
                if stmt.startswith('CREATE TABLE'):
                    table_name = stmt.split('CREATE TABLE IF NOT EXISTS')[1].split('(')[0].strip().strip('"')
                    print(f"  Creating table: {table_name}")
                    conn.execute(text(stmt))
                    print(f"  ✅ Table {table_name} created")
        
        print("✅ All tables created successfully!")
        
        # Verify tables exist
        with engine.connect() as conn:
            result = conn.execute(text("SELECT name FROM sqlite_master WHERE type='table'"))
            tables = [row[0] for row in result]
            print(f"📋 Tables in database: {tables}")
            
            # Test basic operations
            conn.execute(text("SELECT COUNT(*) FROM trades"))
            conn.execute(text("SELECT COUNT(*) FROM KeyValueStore"))
            conn.execute(text("SELECT COUNT(*) FROM pairlocks"))
            print("✅ All tables are accessible!")
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating tables: {e}")
        return False

if __name__ == "__main__":
    db_url = os.environ.get('DB_URL')
    if not db_url:
        print("❌ DB_URL environment variable required")
        sys.exit(1)
    
    success = force_create_tables(db_url)
    sys.exit(0 if success else 1)
