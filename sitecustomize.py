# REAL DATABASE FIX: Force real Turso database writes without any mocks
print("REAL DATABASE FIX: Starting real database connection setup...")

import os, sys
from sqlalchemy.pool import NullPool
from sqlalchemy import create_engine as _orig_create_engine
import sqlalchemy

# adjust Python path to include Freqtrade code
sys.path.insert(0, '/freqtrade')
sys.path.insert(1, '/freqtrade/freqtrade')

def setup_real_turso_connection():
    """Setup real Turso database connection and force real writes"""
    try:
        print("REAL DATABASE FIX: Setting up direct Turso connection...")

        import freqtrade.persistence.models as models
        from sqlalchemy.orm import sessionmaker, scoped_session
        from sqlalchemy import create_engine

        # Get the database URL
        db_url = os.environ.get('DB_URL')
        if not db_url:
            print("REAL DATABASE FIX: No DB_URL found")
            return False

        print(f"REAL DATABASE FIX: Using DB URL: {db_url[:50]}...")

        # Create a real engine for Turso
        real_engine = create_engine(
            db_url,
            echo=False,  # Disable SQL logging for cleaner output
            pool_pre_ping=True,
            pool_recycle=300,
            connect_args={
                "timeout": 60,
                "check_same_thread": False
            }
        )

        # Test connection and create tables
        with real_engine.connect() as conn:
            from sqlalchemy import text
            test_result = conn.execute(text("SELECT 1 as test"))
            test_value = test_result.fetchone()[0]
            print(f"REAL DATABASE FIX: ✅ Connection test passed: {test_value}")

            # Force create all tables
            from freqtrade.persistence.models import ModelBase
            ModelBase.metadata.create_all(conn)
            conn.commit()

            # Verify tables exist
            result = conn.execute(text("SELECT name FROM sqlite_master WHERE type='table'"))
            tables = [row[0] for row in result]
            print(f"REAL DATABASE FIX: ✅ Tables created: {tables}")

        # Create real session factory and set it globally
        RealSessionFactory = sessionmaker(bind=real_engine)
        RealSession = scoped_session(RealSessionFactory)

        # Override FreqTrade's database session completely
        models.Trade.session = RealSession
        models.Trade.query = RealSession.query_property()

        # Also set session on KeyValueStore model
        try:
            from freqtrade.persistence.key_value_store import _KeyValueStoreModel
            _KeyValueStoreModel.session = RealSession
            print("REAL DATABASE FIX: ✅ Set session on _KeyValueStoreModel")
        except Exception as kv_e:
            print(f"REAL DATABASE FIX: Failed to set KeyValueStore session: {kv_e}")

        # Set the global engine (ignore _DECL_BASE error)
        try:
            models._DECL_BASE.metadata.bind = real_engine
        except AttributeError:
            # _DECL_BASE doesn't exist, that's fine
            pass

        # Override init_db to use our real engine
        original_init_db = models.init_db
        def real_init_db(db_url, clean_open_orders=False):
            print(f"REAL DATABASE FIX: init_db called, using real engine")
            # Just return success since we already set up the real connection
            return True
        models.init_db = real_init_db

        print("REAL DATABASE FIX: ✅ Real database connection configured successfully")
        return True

    except Exception as e:
        print(f"REAL DATABASE FIX: Failed to setup real database: {e}")
        import traceback
        traceback.print_exc()
        return False

# Apply real database setup
setup_real_turso_connection()

# Override SQLAlchemy create_engine for libsql
def create_engine(*args, **kwargs):
    url = args[0] if args else kwargs.get('url', '')
    if isinstance(url, str) and url.startswith(('sqlite+libsql://', 'libsql://')):
        kwargs.setdefault('poolclass', NullPool)
    return _orig_create_engine(*args, **kwargs)
sqlalchemy.create_engine = create_engine

# Force real database writes by overriding KeyValueStore operations
try:
    import freqtrade.persistence.key_value_store as kvs_module
    from sqlalchemy.orm import sessionmaker
    from sqlalchemy import create_engine

    # Get database URL and create real session
    db_url = os.environ.get('DB_URL')
    if db_url:
        real_engine = create_engine(
            db_url,
            pool_pre_ping=True,
            pool_recycle=300,
            connect_args={"timeout": 60, "check_same_thread": False}
        )
        RealSession = sessionmaker(bind=real_engine)

        # Override set_startup_time to use real database
        original_set_startup_time = kvs_module.set_startup_time
        def real_set_startup_time():
            try:
                from freqtrade.persistence.key_value_store import KeyValueStore
                from datetime import datetime, timezone

                # Use the correct KeyValueStore.store_value method
                current_time = datetime.now(timezone.utc)
                KeyValueStore.store_value("startup_time", current_time)
                KeyValueStore.store_value("bot_start_time", current_time)

                print("REAL DATABASE FIX: ✅ Real startup time written to database using store_value")
                return True
            except Exception as e:
                print(f"REAL DATABASE FIX: Failed to write startup time: {e}")
                import traceback
                traceback.print_exc()
                return False

        kvs_module.set_startup_time = real_set_startup_time
        print("REAL DATABASE FIX: ✅ Overrode set_startup_time with real database writes")

except Exception as e:
    print(f"REAL DATABASE FIX: Failed to override KeyValueStore operations: {e}")

print("REAL DATABASE FIX: ✅ Setup complete - all database operations will use real Turso database")
