#!/usr/bin/env python3
"""
Comprehensive fix for Freqtrade database and trading issues
This script will:
1. Create database tables properly in Turso
2. Initialize dry-run wallet balance
3. Verify everything is working
"""

import os
import sys
import time
import json
from sqlalchemy import create_engine, text, inspect
from sqlalchemy.exc import OperationalError

def fix_database_and_trading(db_url, config_path):
    """Fix database tables and trading configuration"""
    print(f"🔧 Starting comprehensive fix for database and trading...")
    print(f"Database URL: {db_url}")
    print(f"Config path: {config_path}")
    
    # Step 1: Create engine with specific Turso settings
    print("\n📡 Creating database connection...")
    engine = create_engine(
        db_url,
        echo=False,
        pool_pre_ping=True,
        pool_recycle=3600,
        connect_args={
            "timeout": 30,
            "check_same_thread": False
        }
    )
    
    # Step 2: Create tables using raw SQL with explicit transactions
    print("\n🗄️ Creating database tables...")
    
    # Define complete table schemas
    table_schemas = {
        'trades': '''
            CREATE TABLE IF NOT EXISTS trades (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                exchange VARCHAR(25) NOT NULL,
                pair VARCHAR(25) NOT NULL,
                base_currency VARCHAR(25),
                stake_currency VARCHAR(25),
                is_open BOOLEAN NOT NULL DEFAULT 1,
                fee_open FLOAT,
                fee_open_cost FLOAT,
                fee_open_currency VARCHAR(25),
                fee_close FLOAT,
                fee_close_cost FLOAT,
                fee_close_currency VARCHAR(25),
                open_rate FLOAT,
                open_rate_requested FLOAT,
                open_trade_value FLOAT,
                close_rate FLOAT,
                close_rate_requested FLOAT,
                realized_profit FLOAT,
                close_profit FLOAT,
                close_profit_abs FLOAT,
                stake_amount FLOAT NOT NULL,
                max_stake_amount FLOAT,
                amount FLOAT,
                amount_requested FLOAT,
                open_date DATETIME NOT NULL,
                close_date DATETIME,
                stop_loss FLOAT,
                stop_loss_pct FLOAT,
                initial_stop_loss FLOAT,
                initial_stop_loss_pct FLOAT,
                is_stop_loss_trailing BOOLEAN,
                max_rate FLOAT,
                min_rate FLOAT,
                exit_reason VARCHAR(100),
                exit_order_status VARCHAR(100),
                strategy VARCHAR(100),
                enter_tag VARCHAR(100),
                timeframe INTEGER,
                trading_mode VARCHAR(100),
                amount_precision FLOAT,
                price_precision FLOAT,
                precision_mode INTEGER,
                precision_mode_price INTEGER,
                contract_size FLOAT,
                leverage FLOAT,
                is_short BOOLEAN,
                liquidation_price FLOAT,
                interest_rate FLOAT,
                funding_fees FLOAT,
                funding_fee_running FLOAT,
                record_version INTEGER DEFAULT 1
            )
        ''',
        'KeyValueStore': '''
            CREATE TABLE IF NOT EXISTS "KeyValueStore" (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                "key" VARCHAR(255) NOT NULL UNIQUE,
                value_type VARCHAR(255),
                string_value TEXT,
                datetime_value DATETIME,
                float_value FLOAT,
                int_value INTEGER
            )
        ''',
        'pairlocks': '''
            CREATE TABLE IF NOT EXISTS pairlocks (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                pair VARCHAR(25) NOT NULL,
                reason VARCHAR(255),
                lock_time DATETIME NOT NULL,
                lock_end_time DATETIME NOT NULL,
                active BOOLEAN NOT NULL DEFAULT 1
            )
        '''
    }
    
    # Create tables with explicit transaction handling
    try:
        with engine.begin() as conn:  # Use begin() for explicit transaction
            for table_name, schema in table_schemas.items():
                print(f"  Creating table: {table_name}")
                result = conn.execute(text(schema))
                print(f"  ✅ Table {table_name} created successfully")
        
        print("✅ All tables created successfully!")
        
    except Exception as e:
        print(f"❌ Error creating tables: {e}")
        return False
    
    # Step 3: Verify tables exist
    print("\n🔍 Verifying tables exist...")
    try:
        inspector = inspect(engine)
        existing_tables = set(inspector.get_table_names())
        print(f"Existing tables: {existing_tables}")
        
        required_tables = set(table_schemas.keys())
        missing_tables = required_tables - existing_tables
        
        if missing_tables:
            print(f"❌ Missing tables: {missing_tables}")
            return False
        else:
            print("✅ All required tables exist!")
            
    except Exception as e:
        print(f"❌ Error verifying tables: {e}")
        return False
    
    # Step 4: Test database operations
    print("\n🧪 Testing database operations...")
    try:
        with engine.begin() as conn:
            # Test KeyValueStore
            conn.execute(text("INSERT OR REPLACE INTO KeyValueStore (\"key\", value_type, string_value) VALUES ('test_key', 'string', 'test_value')"))
            result = conn.execute(text("SELECT string_value FROM KeyValueStore WHERE \"key\" = 'test_key'"))
            value = result.scalar()
            if value == 'test_value':
                print("✅ KeyValueStore table working!")
            else:
                print("❌ KeyValueStore test failed")
                return False
                
            # Test trades table
            conn.execute(text("SELECT COUNT(*) FROM trades"))
            print("✅ Trades table working!")
            
    except Exception as e:
        print(f"❌ Database operation test failed: {e}")
        return False
    
    # Step 5: Initialize dry-run wallet in database
    print("\n💰 Initializing dry-run wallet...")
    try:
        with engine.begin() as conn:
            # Set dry-run wallet balance in KeyValueStore
            conn.execute(text("""
                INSERT OR REPLACE INTO KeyValueStore ("key", value_type, float_value) 
                VALUES ('dry_run_wallet_USD', 'float', 10000.0)
            """))
            print("✅ Dry-run wallet initialized with $10,000 USD")
            
    except Exception as e:
        print(f"❌ Error initializing wallet: {e}")
        return False
    
    print("\n🎉 Database and trading fix completed successfully!")
    return True

if __name__ == "__main__":
    # Get database URL from environment or command line
    db_url = os.environ.get('DB_URL')
    if not db_url and len(sys.argv) > 1:
        db_url = sys.argv[1]
    
    if not db_url:
        print("❌ Error: DB_URL environment variable or command line argument required")
        sys.exit(1)
    
    config_path = "/freqtrade/config.json"
    
    success = fix_database_and_trading(db_url, config_path)
    sys.exit(0 if success else 1)
