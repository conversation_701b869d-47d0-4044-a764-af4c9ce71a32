nohup: ignoring input
Using TURSO_CMD: /root/.turso/turso
Firebase Admin SDK initialized with service account
Firebase was initialized by the main application. auth.js will use the existing instance.
Using FREQTRADE_IMAGE: freqtradeorg/freqtrade:stable
CRITICAL: All new bots will use the patched image: freqtradeorg/freqtrade:stable
Ensuring bot instance base directory exists: /root/freqtrade-instances
=================================================
 Freqtrade Bot Manager (Shared Data Provision)
-------------------------------------------------
 Service Listening on: http://0.0.0.0:3001
 Bot Instance Base Dir: /root/freqtrade-instances
 Main Strategies Source: /root/freqtrade/user_data/strategies
 SHARED Data Directory: /root/freqtrade_shared_data
 Host Freqtrade Needed: NO (for provisioning) / YES (for managing shared data)
=================================================
Ensuring shared strategies directory exists (for fallback): /root/bot-manager/freqtrade-shared/strategies
Checking main strategies source directory: /root/freqtrade/user_data/strategies
Main strategies source directory found.
Ensuring base shared data directory exists: /root/freqtrade_shared_data
Base directories ensured/checked.
ValidationError: The 'X-Forwarded-For' header is set but the Express 'trust proxy' setting is false (default). This could indicate a misconfiguration which would prevent express-rate-limit from accurately identifying users. See https://express-rate-limit.github.io/ERR_ERL_UNEXPECTED_X_FORWARDED_FOR/ for more information.
    at Object.xForwardedForHeader (/root/bot-manager/node_modules/express-rate-limit/dist/index.cjs:187:13)
    at wrappedValidations.<computed> [as xForwardedForHeader] (/root/bot-manager/node_modules/express-rate-limit/dist/index.cjs:398:22)
    at Object.keyGenerator (/root/bot-manager/node_modules/express-rate-limit/dist/index.cjs:671:20)
    at /root/bot-manager/node_modules/express-rate-limit/dist/index.cjs:724:32
    at async /root/bot-manager/node_modules/express-rate-limit/dist/index.cjs:704:5 {
  code: 'ERR_ERL_UNEXPECTED_X_FORWARDED_FOR',
  help: 'https://express-rate-limit.github.io/ERR_ERL_UNEXPECTED_X_FORWARDED_FOR/'
}
Authentication requested for path: /provision
Auth header format: Bearer token
Firebase verification successful for user: Js1Gaz4sMPPiDNgFbmAgDFLe4je2
[2025-05-27T01:51:34.928Z] [Route /provision] ==========================================
[2025-05-27T01:51:34.928Z] [Route /provision] NEW PROVISIONING REQUEST RECEIVED
[2025-05-27T01:51:34.928Z] [Route /provision] ==========================================
[2025-05-27T01:51:34.928Z] [Route /provision] Request body: {
  "instanceId": "anshjarvis2003-bot2",
  "port": 8195,
  "userId": "<EMAIL>",
  "apiUsername": "admin",
  "apiPassword": "password"
}
[2025-05-27T01:51:34.928Z] [Route /provision] User info: {
  "id": "Js1Gaz4sMPPiDNgFbmAgDFLe4je2",
  "email": "<EMAIL>"
}
[2025-05-27T01:51:34.928Z] [Route /provision] Extracted parameters:
[2025-05-27T01:51:34.928Z] [Route /provision]   instanceId: anshjarvis2003-bot2
[2025-05-27T01:51:34.928Z] [Route /provision]   port: 8195
[2025-05-27T01:51:34.928Z] [Route /provision]   userId: Js1Gaz4sMPPiDNgFbmAgDFLe4je2
[2025-05-27T01:51:34.928Z] [Route /provision]   apiUsername: admin
[2025-05-27T01:51:34.928Z] [Route /provision]   apiPassword: [PROVIDED]
[2025-05-27T01:51:34.928Z] [Route /provision] Validating parameters...
[2025-05-27T01:51:34.928Z] [Route /provision] ✓ Parameter validation passed
[2025-05-27T01:51:34.928Z] [Route /provision] Setting up directories...
[2025-05-27T01:51:34.928Z] [Route /provision] User directory: /root/freqtrade-instances/Js1Gaz4sMPPiDNgFbmAgDFLe4je2
[2025-05-27T01:51:34.928Z] [Route /provision] ✓ User directory ensured
[2025-05-27T01:51:34.928Z] [Route /provision] Instance directory: /root/freqtrade-instances/Js1Gaz4sMPPiDNgFbmAgDFLe4je2/anshjarvis2003-bot2
[2025-05-27T01:51:34.928Z] [Route /provision] ✓ Instance directory available
[2025-05-27T01:51:34.928Z] [Route /provision] Creating provisioning task...
[2025-05-27T01:51:34.928Z] [Route /provision] Adding task to queue...
[2025-05-27T01:51:34.928Z] [Route /provision] ✓ Task queued. Queue length: 1
[2025-05-27T01:51:34.928Z] [Route /provision] Current queue processing status: isProvisioning = false
[2025-05-27T01:51:34.928Z] [Route /provision] ✓ Sent 202 Accepted response to client
[2025-05-27T01:51:34.928Z] [Route /provision] Starting queue processing...
[2025-05-27T01:51:34.937Z] [Queue Processor] processProvisioningQueue called. isProvisioning: false, Queue length: 1
[2025-05-27T01:51:34.937Z] [Queue Processor] Dequeued task. Params: {
  "instanceId": "anshjarvis2003-bot2",
  "port": 8195,
  "userId": "Js1Gaz4sMPPiDNgFbmAgDFLe4je2",
  "apiUsername": "admin",
  "apiPassword": "password"
}
[2025-05-27T01:51:34.937Z] [anshjarvis2003-bot2] PROVISIONING START - userId: Js1Gaz4sMPPiDNgFbmAgDFLe4je2, port: 8195
[2025-05-27T01:51:34.928Z] [Route /provision] PROVISIONING REQUEST HANDLING COMPLETE
[2025-05-27T01:51:34.928Z] [Route /provision] ==========================================
[anshjarvis2003-bot2] STEP 0: Directory paths defined:
    userDir: /root/freqtrade-instances/Js1Gaz4sMPPiDNgFbmAgDFLe4je2
    instanceDir: /root/freqtrade-instances/Js1Gaz4sMPPiDNgFbmAgDFLe4je2/anshjarvis2003-bot2
    userDataDir: /root/freqtrade-instances/Js1Gaz4sMPPiDNgFbmAgDFLe4je2/anshjarvis2003-bot2/user_data
    instanceStrategiesDir: /root/freqtrade-instances/Js1Gaz4sMPPiDNgFbmAgDFLe4je2/anshjarvis2003-bot2/user_data/strategies
    instanceDataDir: /root/freqtrade-instances/Js1Gaz4sMPPiDNgFbmAgDFLe4je2/anshjarvis2003-bot2/user_data/data
[anshjarvis2003-bot2] STEP 1: Creating instance directories...
[anshjarvis2003-bot2] ✓ User directory created: /root/freqtrade-instances/Js1Gaz4sMPPiDNgFbmAgDFLe4je2
[anshjarvis2003-bot2] ✓ Instance directory created: /root/freqtrade-instances/Js1Gaz4sMPPiDNgFbmAgDFLe4je2/anshjarvis2003-bot2
[anshjarvis2003-bot2] ✓ User data directory created: /root/freqtrade-instances/Js1Gaz4sMPPiDNgFbmAgDFLe4je2/anshjarvis2003-bot2/user_data
[anshjarvis2003-bot2] ✓ Strategies directory created: /root/freqtrade-instances/Js1Gaz4sMPPiDNgFbmAgDFLe4je2/anshjarvis2003-bot2/user_data/strategies
[anshjarvis2003-bot2] ✓ Logs directory created: /root/freqtrade-instances/Js1Gaz4sMPPiDNgFbmAgDFLe4je2/anshjarvis2003-bot2/user_data/logs
[anshjarvis2003-bot2] ✓ Data directory created: /root/freqtrade-instances/Js1Gaz4sMPPiDNgFbmAgDFLe4je2/anshjarvis2003-bot2/user_data/data
[anshjarvis2003-bot2] STEP 1 COMPLETE: Instance directories created.
[anshjarvis2003-bot2] STEP 2: Setting permissions on user data directory...
[anshjarvis2003-bot2] ✓ Permissions set successfully on /root/freqtrade-instances/Js1Gaz4sMPPiDNgFbmAgDFLe4je2/anshjarvis2003-bot2/user_data
[anshjarvis2003-bot2] STEP 2 COMPLETE: Permissions set.
[anshjarvis2003-bot2] STEP 3: Copying strategies from /root/freqtrade/user_data/strategies to /root/freqtrade-instances/Js1Gaz4sMPPiDNgFbmAgDFLe4je2/anshjarvis2003-bot2/user_data/strategies...
[anshjarvis2003-bot2] ✓ Source strategies directory exists, starting copy...
[anshjarvis2003-bot2] ✓ Strategies copied successfully.
[anshjarvis2003-bot2] Copied strategy files: AggressiveSophisticated1m.py, EmaRsiStrategy.py, ExtremeStrategy.py, HighFrequencyScalp1m.py, HighFrequencyStrategy.py, MyStrategy.py, balancedStrat.py, newstrat.py
[anshjarvis2003-bot2] STEP 3 COMPLETE: Strategy copying finished.
[anshjarvis2003-bot2] STEP 4: Creating base configuration...
[anshjarvis2003-bot2] Available strategy files: AggressiveSophisticated1m.py, EmaRsiStrategy.py, ExtremeStrategy.py, HighFrequencyScalp1m.py, HighFrequencyStrategy.py, MyStrategy.py, balancedStrat.py, newstrat.py
[anshjarvis2003-bot2] Selected EmaRsiStrategy
[anshjarvis2003-bot2] Generated config with strategy: EmaRsiStrategy, port: 8195
[anshjarvis2003-bot2] ✓ Base config.json written to: /root/freqtrade-instances/Js1Gaz4sMPPiDNgFbmAgDFLe4je2/anshjarvis2003-bot2/config.json
[anshjarvis2003-bot2] STEP 4 COMPLETE: Base configuration created.
[anshjarvis2003-bot2] STEP 5: Provisioning Turso database...
[anshjarvis2003-bot2] ✓ TURSO_API_KEY available, proceeding with database provisioning...
[anshjarvis2003-bot2] Sanitized Turso DB name: 'bot-js1gaz4smppidngfbmagdfle4je2-anshjarvis2003-bot2' (from 'bot-Js1Gaz4sMPPiDNgFbmAgDFLe4je2-anshjarvis2003-bot2')
[anshjarvis2003-bot2] Creating Turso DB 'bot-js1gaz4smppidngfbmagdfle4je2-anshjarvis2003-bot2' in region 'aws-us-east-1'...
[anshjarvis2003-bot2] turso create stdout: ⣾  Creating database bot-js1gaz4smppidngfbmagdfle4je2-anshjarvis2003-bot2 in group default...

[anshjarvis2003-bot2] turso create stdout: Created database bot-js1gaz4smppidngfbmagdfle4je2-anshjarvis2003-bot2 at group default in 811ms.

Start an interactive SQL shell with:

   turso db shell bot-js1gaz4smppidngfbmagdfle4je2-anshjarvis2003-bot2

To see information about the database, including a connection URL, run:

   turso db show bot-js1gaz4smppidngfbmagdfle4je2-anshjarvis2003-bot2

To get an authentication token for the database, run:

   turso db tokens create bot-js1gaz4smppidngfbmagdfle4je2-anshjarvis2003-bot2


[anshjarvis2003-bot2] ✓ Turso DB 'bot-js1gaz4smppidngfbmagdfle4je2-anshjarvis2003-bot2' created successfully.
[anshjarvis2003-bot2] Fetching Turso DB URL...
[anshjarvis2003-bot2] Trying CLI method to get DB URL...
[anshjarvis2003-bot2] turso show stdout: libsql://bot-js1gaz4smppidngfbmagdfle4je2-anshjarvis2003-bot2-admin0.aws-us-east-1.turso.io

[anshjarvis2003-bot2] ✓ Retrieved rawUrl via CLI: libsql://bot-js1gaz4smppidngfbmagdfle4je2-anshjarvis2003-bot2-admin0.aws-us-east-1.turso.io
[anshjarvis2003-bot2] Formatting database URL for SQLAlchemy...
[anshjarvis2003-bot2] ✓ Formatted URL via formatDbUrl function: sqlite+libsql:///?url=libsql%3A%2F%2F%3AeyJhbGciOiJFZERTQSIsInR5cCI6IkpXVCJ9.eyJqdGkiOiIzR09JU0RIMkVmQ2FaUjdmQlczOTFRIn0.HHLaGF6-ZLhbcdRiTFPWN6OXDKIreIlAzgpC-agv0hAYmHmJgXiiZPOKm-3qTj3zwLEO4aQULgp965PtVfrSAg%40bot-js1gaz4smppidngfbmagdfle4je2-anshjarvis2003-bot2-admin0.aws-us-east-1.turso.io%3A443%2Fbot-js1gaz4smppidngfbmagdfle4je2-anshjarvis2003-bot2&timeout=30
[anshjarvis2003-bot2] Updating config.json with database URL...
[anshjarvis2003-bot2] ✓ config.json updated with Turso db_url: sqlite+libsql:///?url=libsql%3A%2F%2F%3AeyJhbGciOiJFZERTQSIsInR5cCI6IkpXVCJ9.eyJqdGkiOiIzR09JU0RIMkVmQ2FaUjdmQlczOTFRIn0.HHLaGF6-ZLhbcdRiTFPWN6OXDKIreIlAzgpC-agv0hAYmHmJgXiiZPOKm-3qTj3zwLEO4aQULgp965PtVfrSAg%40bot-js1gaz4smppidngfbmagdfle4je2-anshjarvis2003-bot2-admin0.aws-us-east-1.turso.io%3A443%2Fbot-js1gaz4smppidngfbmagdfle4je2-anshjarvis2003-bot2&timeout=30
[anshjarvis2003-bot2] STEP 5.5: Creating Freqtrade tables in Turso database...
[anshjarvis2003-bot2] Creating Freqtrade tables in Turso database: bot-js1gaz4smppidngfbmagdfle4je2-anshjarvis2003-bot2
[anshjarvis2003-bot2] ✓ All Freqtrade tables created successfully in bot-js1gaz4smppidngfbmagdfle4je2-anshjarvis2003-bot2
[anshjarvis2003-bot2] Verifying tables were created...
[anshjarvis2003-bot2] ✓ Tables in database: NAME              
trades                
sqlite_sequence       
KeyValueStore         
pairlocks             
orders                
trade_custom_data
[anshjarvis2003-bot2] ✓ Freqtrade tables created in Turso database
[anshjarvis2003-bot2] STEP 5 COMPLETE: Turso database provisioned successfully.
[anshjarvis2003-bot2] Final DB URL after Turso provisioning: sqlite+libsql:///?url=libsql%3A%2F%2F%3AeyJhbGciOiJFZERTQSIsInR5cCI6IkpXVCJ9.eyJqdGkiOiIzR09JU0RIMkVmQ2FaUjdmQlczOTFRIn0.HHLaGF6-ZLhbcdRiTFPWN6OXDKIreIlAzgpC-agv0hAYmHmJgXiiZPOKm-3qTj3zwLEO4aQULgp965PtVfrSAg%40bot-js1gaz4smppidngfbmagdfle4je2-anshjarvis2003-bot2-admin0.aws-us-east-1.turso.io%3A443%2Fbot-js1gaz4smppidngfbmagdfle4je2-anshjarvis2003-bot2&timeout=30
[anshjarvis2003-bot2] STEP 6: Preparing Docker container...
[anshjarvis2003-bot2] Exchange name: kraken
[anshjarvis2003-bot2] Checking shared data path: /root/freqtrade_shared_data/kraken
[anshjarvis2003-bot2] ✓ Shared data directory exists: /root/freqtrade_shared_data/kraken
[anshjarvis2003-bot2] Container name: freqtrade-anshjarvis2003-bot2
[anshjarvis2003-bot2] Removing any existing container 'freqtrade-anshjarvis2003-bot2' (if exists)...
[2025-05-27T01:51:37.487Z] [Docker Helper] Starting command: docker rm -f freqtrade-anshjarvis2003-bot2 
[2025-05-27T01:51:37.487Z] [Docker Helper] stderr: Error: No such container: freqtrade-anshjarvis2003-bot2
[2025-05-27T01:51:37.487Z] [Docker Helper] Command finished: docker rm -f freqtrade-anshjarvis2003-bot2 (Exit Code: 0)
[2025-05-27T01:51:37.487Z] [Docker Helper] Command succeeded.
[anshjarvis2003-bot2] ✓ Removed existing container 'freqtrade-anshjarvis2003-bot2'.
[anshjarvis2003-bot2] Creating entrypoint script and docker-compose configuration...
[anshjarvis2003-bot2] Mount paths:
      userDataHostPath: /root/freqtrade-instances/Js1Gaz4sMPPiDNgFbmAgDFLe4je2/anshjarvis2003-bot2/user_data
      configHostPath: /root/freqtrade-instances/Js1Gaz4sMPPiDNgFbmAgDFLe4je2/anshjarvis2003-bot2/config.json
      entrypointHostPath: /root/freqtrade-instances/Js1Gaz4sMPPiDNgFbmAgDFLe4je2/anshjarvis2003-bot2/entrypoint.sh
      hostSharedExchangeDataPath: /root/freqtrade_shared_data/kraken
[anshjarvis2003-bot2] ✓ Entrypoint script written to: /root/freqtrade-instances/Js1Gaz4sMPPiDNgFbmAgDFLe4je2/anshjarvis2003-bot2/entrypoint.sh
[anshjarvis2003-bot2] ✓ docker-compose.yml written to: /root/freqtrade-instances/Js1Gaz4sMPPiDNgFbmAgDFLe4je2/anshjarvis2003-bot2/docker-compose.yml
[anshjarvis2003-bot2] STEP 7: Starting Docker container...
[anshjarvis2003-bot2] Attempting to start container via docker-compose...
[2025-05-27T01:51:37.515Z] [Docker Helper] Starting command: docker-compose -f /root/freqtrade-instances/Js1Gaz4sMPPiDNgFbmAgDFLe4je2/anshjarvis2003-bot2/docker-compose.yml up -d (in /root/freqtrade-instances/Js1Gaz4sMPPiDNgFbmAgDFLe4je2/anshjarvis2003-bot2)
[2025-05-27T01:51:37.515Z] [Docker Helper] stderr: Creating freqtrade-anshjarvis2003-bot2 ...
[2025-05-27T01:51:37.515Z] [Docker Helper] stderr: Creating freqtrade-anshjarvis2003-bot2 ... done
[2025-05-27T01:51:37.515Z] [Docker Helper] Command finished: docker-compose -f /root/freqtrade-instances/Js1Gaz4sMPPiDNgFbmAgDFLe4je2/anshjarvis2003-bot2/docker-compose.yml up -d (Exit Code: 0)
[2025-05-27T01:51:37.515Z] [Docker Helper] Command succeeded.
[anshjarvis2003-bot2] ✓ Container started successfully via 'docker-compose'.
[anshjarvis2003-bot2] STEP 7 COMPLETE: Docker container started.
[anshjarvis2003-bot2] STEP 8: Verifying container status...
[2025-05-27T01:51:38.082Z] [Docker Helper] Starting command: docker ps -f name=freqtrade-anshjarvis2003-bot2 --format table {{.Names}}\t{{.Status}} 
[2025-05-27T01:51:38.082Z] [Docker Helper] stdout: NAMES
[2025-05-27T01:51:38.082Z] [Docker Helper] stdout: 
[2025-05-27T01:51:38.082Z] [Docker Helper] stdout: 
[2025-05-27T01:51:38.082Z] [Docker Helper] stdout: 
[2025-05-27T01:51:38.082Z] [Docker Helper] stdout: 
[2025-05-27T01:51:38.082Z] [Docker Helper] stdout: STATUS
[2025-05-27T01:51:38.082Z] [Docker Helper] stdout: 
[2025-05-27T01:51:38.082Z] [Docker Helper] stdout: freqtrade-anshjarvis2003-bot2
[2025-05-27T01:51:38.082Z] [Docker Helper] stdout: 
[2025-05-27T01:51:38.082Z] [Docker Helper] stdout: Up Less than a second
[2025-05-27T01:51:38.082Z] [Docker Helper] stdout: 
[2025-05-27T01:51:38.082Z] [Docker Helper] Command finished: docker ps -f name=freqtrade-anshjarvis2003-bot2 --format table {{.Names}}\t{{.Status}} (Exit Code: 0)
[2025-05-27T01:51:38.082Z] [Docker Helper] Final stdout: NAMES                           STATUS
freqtrade-anshjarvis2003-bot2   Up Less than a second
[2025-05-27T01:51:38.082Z] [Docker Helper] Command succeeded.
[anshjarvis2003-bot2] Container status: NAMES                           STATUS
freqtrade-anshjarvis2003-bot2   Up Less than a second
[anshjarvis2003-bot2] ✓ Container is running.
[anshjarvis2003-bot2] STEP 8 COMPLETE: Container verification finished.
[anshjarvis2003-bot2] ==========================================
[anshjarvis2003-bot2] PROVISIONING COMPLETE - SUCCESS!
[anshjarvis2003-bot2] Summary:
[anshjarvis2003-bot2]   User ID: Js1Gaz4sMPPiDNgFbmAgDFLe4je2
[anshjarvis2003-bot2]   Instance ID: anshjarvis2003-bot2
[anshjarvis2003-bot2]   Port: 8195
[anshjarvis2003-bot2]   Container: freqtrade-anshjarvis2003-bot2
[anshjarvis2003-bot2]   Strategy: EmaRsiStrategy
[anshjarvis2003-bot2]   Database URL: sqlite+libsql:///?url=libsql%3A%2F%2F%3AeyJhbGciOiJFZERTQSIsInR5cCI6IkpXVCJ9.eyJqdGkiOiIzR09JU0RIMkVmQ2FaUjdmQlczOTFRIn0.HHLaGF6-ZLhbcdRiTFPWN6OXDKIreIlAzgpC-agv0hAYmHmJgXiiZPOKm-3qTj3zwLEO4aQULgp965PtVfrSAg%40bot-js1gaz4smppidngfbmagdfle4je2-anshjarvis2003-bot2-admin0.aws-us-east-1.turso.io%3A443%2Fbot-js1gaz4smppidngfbmagdfle4je2-anshjarvis2003-bot2&timeout=30
[anshjarvis2003-bot2]   Instance Directory: /root/freqtrade-instances/Js1Gaz4sMPPiDNgFbmAgDFLe4je2/anshjarvis2003-bot2
[anshjarvis2003-bot2] ==========================================
[anshjarvis2003-bot2] Headers already sent, cannot send response.
[2025-05-27T01:51:38.109Z] [anshjarvis2003-bot2] Entering FINALLY block - cleaning up...
[2025-05-27T01:51:38.109Z] [anshjarvis2003-bot2] Set isProvisioning = false
[2025-05-27T01:51:38.109Z] [anshjarvis2003-bot2] PROVISIONING PROCESS FINISHED.
[2025-05-27T01:51:38.109Z] [anshjarvis2003-bot2] Queue length now: 0
[2025-05-27T01:51:38.109Z] [anshjarvis2003-bot2] Scheduling next queue processing in 500ms...
[2025-05-27T01:51:38.610Z] [Queue Processor] processProvisioningQueue called. isProvisioning: false, Queue length: 0
[2025-05-27T01:51:38.610Z] [Queue Processor] Exiting early - isProvisioning: false, queue empty: true
Authentication requested for path: /restart/anshjarvis2003-bot2
Auth header format: Bearer token
Firebase verification successful for user: Js1Gaz4sMPPiDNgFbmAgDFLe4je2
[Route /restart/anshjarvis2003-bot2] Received POST. Restarting container freqtrade-anshjarvis2003-bot2
[2025-05-27T01:51:48.563Z] [Docker Helper] Starting command: docker restart freqtrade-anshjarvis2003-bot2 
[2025-05-27T01:51:48.563Z] [Docker Helper] stdout: freqtrade-anshjarvis2003-bot2
[2025-05-27T01:51:48.563Z] [Docker Helper] Command finished: docker restart freqtrade-anshjarvis2003-bot2 (Exit Code: 0)
[2025-05-27T01:51:48.563Z] [Docker Helper] Final stdout: freqtrade-anshjarvis2003-bot2
[2025-05-27T01:51:48.563Z] [Docker Helper] Command succeeded.
[Route /restart/anshjarvis2003-bot2] Restarted.
