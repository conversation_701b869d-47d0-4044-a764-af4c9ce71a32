#!/usr/bin/env python3

import os
import sys
import subprocess
import time

def main():
    """Comprehensive entrypoint that forces Freqtrade to use Turso database"""

    print("🚀 STARTING FREQTRADE WITH TURSO DATABASE INTEGRATION")
    print("✅ Forcing database URL through command line argument")
    print("✅ Configuration verified with 1M USD dry run wallet")

    # Get database URL from environment
    db_url = os.environ.get('DB_URL')
    if not db_url:
        print("❌ DB_URL environment variable not set")
        sys.exit(1)

    print(f"🔗 Using database URL: {db_url[:50]}...")

    # Start Freqtrade with explicit database URL
    freqtrade_cmd = [
        'freqtrade', 'trade',
        '--config', '/freqtrade/config.json',
        '--db-url', db_url,
        '--strategy-path', '/freqtrade/user_data/strategies'
    ]

    print(f"🎯 Executing: freqtrade trade --config /freqtrade/config.json --db-url [TURSO_URL] --strategy-path /freqtrade/user_data/strategies")

    try:
        # Use exec to replace the current process with the full command
        os.execv('/usr/local/bin/freqtrade', freqtrade_cmd)
    except Exception as e:
        print(f"❌ Failed to start Freqtrade: {e}")
        print(f"Command was: {freqtrade_cmd}")
        sys.exit(1)

if __name__ == '__main__':
    main()
