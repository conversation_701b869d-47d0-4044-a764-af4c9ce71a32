#!/bin/sh
set -e

echo "[ENTRYPOINT] Starting FreqTrade container initialization..."
echo "[ENTRYPOINT] Database URL: $DB_URL"

# Enhanced database initialization with comprehensive error handling
python3 - << 'PYCODE'
import os
import time
import sys
from sqlalchemy import create_engine, inspect, text
from sqlalchemy.exc import OperationalError as SAOperationalError
import sqlite3

def ensure_database_tables(db_url, max_retries=5, retry_delay=2):
    """
    Ensure all FreqTrade database tables exist with aggressive retry logic and direct SQL creation
    """
    print(f"[ENTRYPOINT] Ensuring database tables exist for: {db_url}")

    # Define critical table schemas directly
    table_schemas = {
        'KeyValueStore': '''
            CREATE TABLE IF NOT EXISTS "KeyValueStore" (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                "key" VARCHAR(255) NOT NULL UNIQUE,
                value_type VARCHAR(255),
                string_value TEXT,
                datetime_value DATETIME,
                float_value FLOAT,
                int_value INTEGER
            )
        ''',
        'trades': '''
            CREATE TABLE IF NOT EXISTS trades (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                exchange VARCHAR(25) NOT NULL,
                pair VARCHAR(25) NOT NULL,
                base_currency VARCHAR(25),
                stake_currency VARCHAR(25),
                is_open BOOLEAN NOT NULL DEFAULT 1,
                fee_open FLOAT,
                fee_open_cost FLOAT,
                fee_open_currency VARCHAR(25),
                fee_close FLOAT,
                fee_close_cost FLOAT,
                fee_close_currency VARCHAR(25),
                open_rate FLOAT,
                open_rate_requested FLOAT,
                open_trade_value FLOAT,
                close_rate FLOAT,
                close_rate_requested FLOAT,
                realized_profit FLOAT,
                close_profit FLOAT,
                close_profit_abs FLOAT,
                stake_amount FLOAT NOT NULL,
                max_stake_amount FLOAT,
                amount FLOAT,
                amount_requested FLOAT,
                open_date DATETIME NOT NULL,
                close_date DATETIME,
                stop_loss FLOAT,
                stop_loss_pct FLOAT,
                initial_stop_loss FLOAT,
                initial_stop_loss_pct FLOAT,
                is_stop_loss_trailing BOOLEAN,
                max_rate FLOAT,
                min_rate FLOAT,
                exit_reason VARCHAR(100),
                exit_order_status VARCHAR(100),
                strategy VARCHAR(100),
                enter_tag VARCHAR(100),
                timeframe INTEGER,
                trading_mode VARCHAR(100),
                amount_precision FLOAT,
                price_precision FLOAT,
                precision_mode INTEGER,
                precision_mode_price INTEGER,
                contract_size FLOAT,
                leverage FLOAT,
                is_short BOOLEAN,
                liquidation_price FLOAT,
                interest_rate FLOAT,
                funding_fees FLOAT,
                funding_fee_running FLOAT,
                record_version INTEGER DEFAULT 1
            )
        '''
    }

    for attempt in range(max_retries):
        try:
            print(f"[ENTRYPOINT] Database initialization attempt {attempt + 1}/{max_retries}")

            # Create engine for direct SQL operations
            engine = create_engine(db_url)

            # First, try FreqTrade's init_db with proper table creation
            try:
                from freqtrade.persistence.models import init_db, ModelBase
                print("[ENTRYPOINT] Calling FreqTrade init_db...")
                init_db(db_url)
                print("[ENTRYPOINT] ✓ FreqTrade init_db completed")

                # Force table creation using ModelBase
                print("[ENTRYPOINT] Creating tables using ModelBase.metadata.create_all...")
                ModelBase.metadata.create_all(engine)
                print("[ENTRYPOINT] ✓ ModelBase table creation completed")
            except Exception as init_e:
                print(f"[ENTRYPOINT] FreqTrade init_db failed: {init_e}")

            # Check existing tables
            inspector = inspect(engine)
            existing_tables = set(inspector.get_table_names())
            print(f"[ENTRYPOINT] Existing tables: {existing_tables}")

            # Create missing tables using direct SQL with Turso-specific handling
            missing_tables = set(table_schemas.keys()) - existing_tables
            if missing_tables:
                print(f"[ENTRYPOINT] Creating missing tables with direct SQL: {missing_tables}")

                # Use autocommit mode for Turso compatibility
                with engine.connect() as conn:
                    # Enable autocommit for Turso
                    conn = conn.execution_options(autocommit=True)

                    for table_name in missing_tables:
                        try:
                            print(f"[ENTRYPOINT] Creating table: {table_name}")
                            result = conn.execute(text(table_schemas[table_name]))
                            print(f"[ENTRYPOINT] ✓ Created table: {table_name} (result: {result})")
                        except Exception as create_e:
                            print(f"[ENTRYPOINT] Failed to create table {table_name}: {create_e}")

                # Force a new connection to check tables
                engine.dispose()
                engine = create_engine(db_url)
                inspector = inspect(engine)
                updated_tables = set(inspector.get_table_names())
                print(f"[ENTRYPOINT] Tables after direct creation: {updated_tables}")
            else:
                print("[ENTRYPOINT] ✓ All required tables already exist")

            # Test basic operations on critical tables
            try:
                with engine.connect() as conn:
                    # Test KeyValueStore table
                    try:
                        result = conn.execute(text("SELECT COUNT(*) FROM KeyValueStore"))
                        count = result.scalar()
                        print(f"[ENTRYPOINT] KeyValueStore table test: {count} rows")
                    except Exception as kv_e:
                        print(f"[ENTRYPOINT] KeyValueStore test failed: {kv_e}")

                    # Test trades table
                    try:
                        result = conn.execute(text("SELECT COUNT(*) FROM trades"))
                        count = result.scalar()
                        print(f"[ENTRYPOINT] trades table test: {count} rows")
                    except Exception as trades_e:
                        print(f"[ENTRYPOINT] trades test failed: {trades_e}")

                print("[ENTRYPOINT] ✓ Database table tests completed")
                return True

            except Exception as test_e:
                print(f"[ENTRYPOINT] Database table test failed: {test_e}")
                if attempt < max_retries - 1:
                    print(f"[ENTRYPOINT] Retrying in {retry_delay} seconds...")
                    time.sleep(retry_delay)
                    continue
                else:
                    print("[ENTRYPOINT] WARNING: Database tests failed, but continuing...")
                    return False

        except Exception as e:
            print(f"[ENTRYPOINT] Database initialization failed (attempt {attempt + 1}): {e}")
            if attempt < max_retries - 1:
                print(f"[ENTRYPOINT] Retrying in {retry_delay} seconds...")
                time.sleep(retry_delay)
            else:
                print("[ENTRYPOINT] CRITICAL: All database initialization attempts failed!")
                return False

    return False

# ULTIMATE FIX: Force create tables using direct SQLAlchemy with Freqtrade models
def ultimate_fix_database(db_url):
    """Ultimate fix: Force create tables and ensure they work with Freqtrade"""
    try:
        print("[ENTRYPOINT] ULTIMATE FIX: Starting comprehensive database fix...")

        from sqlalchemy import create_engine, text, MetaData
        from freqtrade.persistence.models import ModelBase, init_db
        from freqtrade.persistence import Trade
        from freqtrade.persistence.key_value_store import _KeyValueStoreModel

        # Create engine with Turso-optimized settings
        engine = create_engine(
            db_url,
            echo=True,  # Enable SQL logging for debugging
            pool_pre_ping=True,
            pool_recycle=300,
            connect_args={
                "timeout": 60,
                "check_same_thread": False
            }
        )

        print("[ENTRYPOINT] ULTIMATE FIX: Testing basic connection...")
        with engine.connect() as conn:
            result = conn.execute(text("SELECT 1"))
            print(f"[ENTRYPOINT] ULTIMATE FIX: ✓ Basic connection works: {result.fetchone()}")

        print("[ENTRYPOINT] ULTIMATE FIX: Calling Freqtrade's init_db...")
        init_db(db_url, clean_open_orders=False)
        print("[ENTRYPOINT] ULTIMATE FIX: ✓ init_db completed")

        print("[ENTRYPOINT] ULTIMATE FIX: Force creating all tables...")
        ModelBase.metadata.create_all(engine, checkfirst=True)
        print("[ENTRYPOINT] ULTIMATE FIX: ✓ ModelBase.metadata.create_all completed")

        # Verify and test tables
        with engine.connect() as conn:
            # Check tables exist
            result = conn.execute(text("SELECT name FROM sqlite_master WHERE type='table'"))
            tables = [row[0] for row in result]
            print(f"[ENTRYPOINT] ULTIMATE FIX: Tables found: {tables}")

            # Test trades table specifically
            if 'trades' in tables:
                try:
                    result = conn.execute(text("SELECT COUNT(*) FROM trades"))
                    count = result.fetchone()[0]
                    print(f"[ENTRYPOINT] ULTIMATE FIX: ✓ trades table accessible, rows: {count}")
                except Exception as e:
                    print(f"[ENTRYPOINT] ULTIMATE FIX: ⚠ trades table not accessible: {e}")
                    return False
            else:
                print("[ENTRYPOINT] ULTIMATE FIX: ⚠ trades table missing")
                return False

            # Test KeyValueStore table
            if 'KeyValueStore' in tables:
                try:
                    result = conn.execute(text("SELECT COUNT(*) FROM KeyValueStore"))
                    count = result.fetchone()[0]
                    print(f"[ENTRYPOINT] ULTIMATE FIX: ✓ KeyValueStore table accessible, rows: {count}")
                except Exception as e:
                    print(f"[ENTRYPOINT] ULTIMATE FIX: ⚠ KeyValueStore table not accessible: {e}")
                    return False
            else:
                print("[ENTRYPOINT] ULTIMATE FIX: ⚠ KeyValueStore table missing")
                return False

        # Test Freqtrade ORM access
        print("[ENTRYPOINT] ULTIMATE FIX: Testing Freqtrade ORM access...")
        try:
            # Test Trade model
            trade_count = Trade.session.query(Trade).count()
            print(f"[ENTRYPOINT] ULTIMATE FIX: ✓ Trade ORM works, count: {trade_count}")

            # Test KeyValueStore model
            kvs_count = _KeyValueStoreModel.session.query(_KeyValueStoreModel).count()
            print(f"[ENTRYPOINT] ULTIMATE FIX: ✓ KeyValueStore ORM works, count: {kvs_count}")

            print("[ENTRYPOINT] ULTIMATE FIX: ✅ ALL TESTS PASSED - DATABASE FULLY FUNCTIONAL")
            return True

        except Exception as e:
            print(f"[ENTRYPOINT] ULTIMATE FIX: ❌ ORM test failed: {e}")
            return False

    except Exception as e:
        print(f"[ENTRYPOINT] ULTIMATE FIX: ❌ CRITICAL FAILURE: {e}")
        import traceback
        traceback.print_exc()
        return False

# Apply the ultimate fix
ultimate_success = ultimate_fix_database(os.environ['DB_URL'])

if ultimate_success:
    print("[ENTRYPOINT] ULTIMATE FIX: ✅ DATABASE COMPLETELY READY - FreqTrade will work properly")
else:
    print("[ENTRYPOINT] ULTIMATE FIX: ❌ DATABASE STILL NOT READY - FreqTrade may have issues")
PYCODE

echo "[ENTRYPOINT] Starting FreqTrade trading..."
exec freqtrade trade --config /freqtrade/config.json --db-url "$DB_URL"
