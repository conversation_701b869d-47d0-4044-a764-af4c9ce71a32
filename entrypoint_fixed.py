#!/usr/bin/env python3

import os
import sys
import subprocess

# Add freqtrade to Python path
sys.path.insert(0, '/freqtrade')

def apply_minimal_patches():
    """Apply minimal patches that don't interfere with wallet functionality"""
    
    print("🔧 Applying minimal database error patches...")
    
    try:
        from freqtrade.persistence import Trade, Order, PairLock
        from freqtrade.persistence.models import PairLocks
        from sqlalchemy.exc import OperationalError
        
        # Only patch database query methods, NOT wallet methods
        def safe_database_operation(original_func, operation_name, default_return):
            def wrapper(*args, **kwargs):
                try:
                    return original_func(*args, **kwargs)
                except OperationalError as e:
                    if "no such table" in str(e):
                        print(f"MINIMAL PATCH: {operation_name}: Table missing, returning default")
                        return default_return
                    raise
            return wrapper
        
        # Patch only critical database query methods
        if hasattr(Trade, 'get_open_trades'):
            original_get_open_trades = Trade.get_open_trades
            Trade.get_open_trades = staticmethod(
                safe_database_operation(original_get_open_trades, "Trade.get_open_trades", [])
            )
            print("✅ Patched Trade.get_open_trades")
        
        if hasattr(Trade, 'get_open_trade_count'):
            original_get_open_trade_count = Trade.get_open_trade_count
            Trade.get_open_trade_count = staticmethod(
                safe_database_operation(original_get_open_trade_count, "Trade.get_open_trade_count", 0)
            )
            print("✅ Patched Trade.get_open_trade_count")
        
        if hasattr(PairLocks, 'get_pair_locks'):
            original_get_pair_locks = PairLocks.get_pair_locks
            PairLocks.get_pair_locks = staticmethod(
                safe_database_operation(original_get_pair_locks, "PairLocks.get_pair_locks", [])
            )
            print("✅ Patched PairLocks.get_pair_locks")
        
        print("✅ Minimal patches applied - wallet functionality preserved")
        
    except ImportError as e:
        print(f"Warning: Could not import modules for patching: {e}")
    except Exception as e:
        print(f"Error during patching: {e}")

def ensure_database_tables():
    """Ensure database tables exist using Freqtrade's built-in initialization"""
    db_url = os.environ.get('DB_URL', '')
    if not db_url:
        return False
    
    try:
        from freqtrade.persistence import init_db
        
        print(f"🔧 Initializing database: {db_url[:50]}...")
        
        # Use Freqtrade's native database initialization
        init_db(db_url, clean_open_orders=False)
        print("✅ Database initialized with Freqtrade's native system")
        
        # Verify tables exist
        from sqlalchemy import create_engine, text
        engine = create_engine(db_url)
        with engine.connect() as conn:
            result = conn.execute(text('SELECT name FROM sqlite_master WHERE type="table"'))
            tables = [row[0] for row in result]
            print(f"📊 Tables available: {tables}")
            
            if 'trades' in tables:
                print("[ENTRYPOINT] ✓ DATABASE READY")
                return True
            else:
                print("[ENTRYPOINT] ⚠ DATABASE NOT READY")
                return False
                
    except Exception as e:
        print(f"❌ Database initialization failed: {e}")
        print("[ENTRYPOINT] ⚠ DATABASE NOT READY")
        return False

def main():
    """Main entrypoint function"""
    print("🚀 Starting Freqtrade with corrected patches...")
    
    # Apply minimal patches that don't break wallet functionality
    apply_minimal_patches()
    
    # Initialize database
    db_ready = ensure_database_tables()
    
    # Start Freqtrade
    print("🎯 Starting Freqtrade...")
    
    freqtrade_args = [
        'python', '-m', 'freqtrade', 'trade',
        '--config', '/freqtrade/config.json',
        '--db-url', os.environ.get('DB_URL', ''),
        '--strategy-path', '/freqtrade/user_data/strategies'
    ]
    
    try:
        subprocess.run(freqtrade_args, check=True)
    except subprocess.CalledProcessError as e:
        print(f"❌ Freqtrade exited with error: {e}")
        sys.exit(1)
    except KeyboardInterrupt:
        print("🛑 Freqtrade stopped by user")
        sys.exit(0)

if __name__ == '__main__':
    main()
