version: '3.8'

services:
  freqtrade-anshjarvis2003-bot2:
    image: freqtrade-patched:latest
    container_name: freqtrade-anshjarvis2003-bot2
    restart: unless-stopped
    network_mode: host
    entrypoint: ["/freqtrade/entrypoint.sh"]
    environment:
      - DB_URL=sqlite+libsql:///?url=libsql%3A%2F%2F%3AeyJhbGciOiJFZERTQSIsInR5cCI6IkpXVCJ9.eyJqdGkiOiIzR09JU0RIMkVmQ2FaUjdmQlczOTFRIn0.HHLaGF6-ZLhbcdRiTFPWN6OXDKIreIlAzgpC-agv0hAYmHmJgXiiZPOKm-3qTj3zwLEO4aQULgp965PtVfrSAg%40bot-js1gaz4smppidngfbmagdfle4je2-anshjarvis2003-bot2-admin0.aws-us-east-1.turso.io%3A443%2Fbot-js1gaz4smppidngfbmagdfle4je2-anshjarvis2003-bot2&timeout=30
      - LOGFILE=/freqtrade/user_data/logs/freqtrade.log
    volumes:
      - /root/freqtrade-instances/Js1Gaz4sMPPiDNgFbmAgDFLe4je2/anshjarvis2003-bot2/entrypoint.sh:/freqtrade/entrypoint.sh:ro
      - /root/freqtrade-instances/Js1Gaz4sMPPiDNgFbmAgDFLe4je2/anshjarvis2003-bot2/user_data:/freqtrade/user_data
      - /root/freqtrade-instances/Js1Gaz4sMPPiDNgFbmAgDFLe4je2/anshjarvis2003-bot2/config.json:/freqtrade/config.json:ro
      - /root/freqtrade_shared_data/kraken:/freqtrade/user_data/data/kraken:ro
